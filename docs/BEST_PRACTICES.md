# Ferox Encryptor 最佳实践指南

本指南提供了在使用 Ferox Encryptor 时最大化安全性和效率的最佳实践。遵循这些建议可以帮助您避免常见错误，并确保您的数据得到最强有力的保护。

## 目录
1. [核心安全原则](#核心安全原则)
2. [选择正确的安全级别](#选择正确的安全级别)
3. [密码和密钥文件管理](#密码和密钥文件管理)
4. [操作流程最佳实践](#操作流程最佳实践)
5. [备份与恢复策略](#备份与恢复策略)
6. [自动化与脚本](#自动化与脚本)
7. [安全清单](#安全清单)

---

### 1. 核心安全原则

- **最小权限原则**: 不要在拥有超出必要权限的用户（如 `root`）下运行加密工具。
- **纵深防御**: 不要只依赖加密。将加密与其他安全措施（如防火墙、安全的操作系统、物理安全）结合起来。
- **攻击面最小化**: 仅在需要时安装和运行此工具。在不使用时，请确保其二进制文件和相关数据（如密钥文件）是安全的。
- **定期审查**: 定期回顾您的加密策略、密码强度和备份程序，以适应新的安全威胁。

### 2. 选择正确的安全级别

选择安全级别是在“便利性”和“偏执度”之间做出的权衡。

- **`Interactive`**: **速度最快**。
  - **适用场景**: 加密低风险或需要频繁访问的文件，如开发环境中的临时日志、个人草稿等。
  - **优点**: 对日常工作流影响最小。
  - **缺点**: 对抗专业、有组织的攻击者的能力最弱。

- **`Moderate`**: **推荐的默认选项**。
  - **适用场景**: 大多数用户的标准选择。适用于保护个人文档、财务报表、私人照片等标准敏感数据。
  - **优点**: 在安全性和性能之间取得了极佳的平衡。
  - **缺点**: 对于拥有国家级资源的攻击者来说，可能仍然不够安全。

- **`Paranoid`**: **最安全但最慢**。
  - **适用场景**: 加密极度敏感的数据，如商业机密、法律文件、长期存档的密码库等。
  - **优点**: 提供最高级别的保护，能有效抵御资金雄厚的攻击者。
  - **缺点**: 加密和解密过程会明显变慢，特别是对于大文件。

**经验法则**: 如果不确定，请使用 `Moderate`。如果文件的重要性值得您多花几秒钟（甚至几分钟）来等待，请使用 `Paranoid`。

### 3. 密码和密钥文件管理

您的加密强度取决于您最薄弱的环节——通常是密码。

#### 密码策略
- **使用密码管理器**: 使用如 Bitwarden, 1Password 或 KeePass 的密码管理器来生成和存储您的密码。人类不擅长创造和记忆真正随机的密码。
- **长度优于复杂性**: 一个长密码短语（例如 `correct-horse-battery-staple`）通常比一个短而复杂的密码（例如 `Tr0ub&d0r`）更安全且更容易记住。推荐至少16个字符。
- **唯一性**: **绝对不要**在多个地方重复使用用于文件加密的密码。

#### 密钥文件策略
密钥文件提供了第二层保护，强烈推荐用于高价值数据。

- **物理隔离**: 将密钥文件存储在与加密文件**完全不同**的物理介质上。例如，将加密文件存储在笔记本电脑上，而将密钥文件存储在加密的U盘中。
- **安全备份**: 像对待密码一样对待您的密钥文件。创建多个备份，并将它们存储在安全、隔离的位置（例如，一个在防火保险箱中，一个在银行保险箱中）。
- **命名混淆**: 不要将密钥文件命名为 `my_super_secret_data.key`。给它一个不起眼的名字，如 `system_config.dat` 或 `archive.zip`。

### 4. 操作流程最佳实践

从加密到解密，遵循一个安全的流程至关重要。

**安全加密流程**:
1.  **隔离环境 (可选)**: 对于极度敏感的数据，考虑在离线或虚拟机环境中进行加密操作。
2.  **验证原件**: 在加密前，使用哈希函数（如 `sha256sum`）计算原始文件的哈希值，并保存该哈希值。
3.  **执行加密**: `ferox-encryptor encrypt "document.docx" --level paranoid`
4.  **验证加密文件**: 确认 `.feroxcrypt` 文件已创建并且大小合理。
5.  **测试解密**: 立即解密文件到一个临时位置，并再次计算其哈希值，与第2步的哈希值进行比较。
    ```bash
    ferox-encryptor decrypt "document.docx.feroxcrypt"
    sha256sum "document.docx"
    # 比较两个哈希值是否完全一致
    ```
6.  **安全删除原件**: 只有在确认加密和解密过程无误后，才安全地删除原始文件。使用 `shred` (Linux) 或 `rm -P` (macOS) 等工具来覆盖文件数据，而不仅仅是删除它。
    ```bash
    # Linux
    shred -uzn 3 "document.docx"
    # macOS
    rm -P "document.docx"
    ```

### 5. 备份与恢复策略

没有经过测试的备份等于没有备份。

- **3-2-1 规则**:
  - 保存至少 **3** 份数据副本。
  - 将副本存储在 **2** 种不同的存储介质上。
  - 将 **1** 份副本保存在异地。
- **同时备份密钥**: 如果您使用密钥文件，请确保您的备份策略也涵盖了密钥文件。将它们与加密文件分开备份。
- **定期恢复测试**: 至少每季度进行一次恢复演练。从您的异地备份中取出一个加密文件和密钥文件，并尝试解密它。这能确保在紧急情况下您的恢复流程是有效的。

### 6. 自动化与脚本

在脚本中使用 Ferox Encryptor 可以提高效率，但也可能引入安全风险。

- **避免在脚本中硬编码密码**:
  - **错误**: `PASSWORD="mysecret" ferox-encryptor ...` (密码会留在命令历史中)
  - **稍好**: 使用环境变量或 `rpassword` 提示。
  - **更好**: 对于完全自动化的脚本，将密码存储在受严密保护的位置（如加密的配置文件或系统密钥环中），并仅在脚本运行时加载。
- **设置严格的文件权限**: 脚本和它们处理的任何临时文件都应具有最严格的权限 (`chmod 700` for scripts, `chmod 600` for files)。
- **使用 `set -euo pipefail`**: 在您的 shell 脚本顶部使用此命令，以确保脚本在遇到错误时会立即失败，而不是继续执行可能导致问题的操作。

### 7. 安全清单

在处理关键数据时，请使用此清单：

**加密前**:
- [ ] 我选择的安全级别是否与数据的敏感性相匹配？
- [ ] 我使用的密码是否足够强大且唯一？
- [ ] 如果使用密钥文件，它是否安全存储并已备份？
- [ ] 我是否已备份了原始文件？

**加密后**:
- [ ] 我是否已验证加密文件可以成功解密？
- [ ] 我是否已验证解密后的文件与原件完全一致（例如，通过哈希校验）？
- [ ] 我是否已**安全地**删除了原始文件？
- [ ] 我是否已将加密文件和任何密钥文件备份到安全、隔离的位置？

---
通过遵循这些最佳实践，您可以充分利用 Ferox Encryptor 提供的强大安全功能，并确保您的数据安全无虞。
