use ferox_encryptor::{run_decryption_flow, run_encryption_flow, Level}; use std::fs; use std::path::PathBuf; use std::sync::{Arc, Mutex}; use tempfile::TempDir; fn main() -> anyhow::Result<()> { let temp_dir = TempDir::new()?; let temp_file_path = Arc::new(Mutex::new(None::<PathBuf>)); let test_file = temp_dir.path().join("test.txt"); fs::write(&test_file, b"test content")?; run_encryption_flow(&test_file, false, "correct", Level::Interactive, None, Arc::clone(&temp_file_path))?; let encrypted_file = temp_dir.path().join("test.txt.feroxcrypt"); fs::remove_file(&test_file)?; let result = run_decryption_flow(&encrypted_file, "wrong", None, Arc::clone(&temp_file_path)); if let Err(e) = result { println!("Wrong password error: {}", e); } Ok(()) }
